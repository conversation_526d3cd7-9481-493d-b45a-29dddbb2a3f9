<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Enable\\Cors\\AdminPage' => $baseDir . '/src/AdminPage.php',
    'Enable\\Cors\\Helpers\\Headers' => $baseDir . '/src/Helpers/Headers.php',
    'Enable\\Cors\\Helpers\\Htaccess' => $baseDir . '/src/Helpers/Htaccess.php',
    'Enable\\Cors\\Helpers\\Option' => $baseDir . '/src/Helpers/Option.php',
    'Enable\\Cors\\Plugin' => $baseDir . '/src/Plugin.php',
    'Enable\\Cors\\SettingsApi' => $baseDir . '/src/SettingsApi.php',
    'Enable\\Cors\\Traits\\Api' => $baseDir . '/src/Traits/Api.php',
    'Enable\\Cors\\Upgrade' => $baseDir . '/src/Upgrade.php',
);
