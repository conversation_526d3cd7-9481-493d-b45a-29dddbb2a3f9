<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit4e2e0fdcba009c0df25206a0075545aa
{
    public static $prefixLengthsPsr4 = array (
        'E' => 
        array (
            'Enable\\Cors\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Enable\\Cors\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Enable\\Cors\\AdminPage' => __DIR__ . '/../..' . '/src/AdminPage.php',
        'Enable\\Cors\\Helpers\\Headers' => __DIR__ . '/../..' . '/src/Helpers/Headers.php',
        'Enable\\Cors\\Helpers\\Htaccess' => __DIR__ . '/../..' . '/src/Helpers/Htaccess.php',
        'Enable\\Cors\\Helpers\\Option' => __DIR__ . '/../..' . '/src/Helpers/Option.php',
        'Enable\\Cors\\Plugin' => __DIR__ . '/../..' . '/src/Plugin.php',
        'Enable\\Cors\\SettingsApi' => __DIR__ . '/../..' . '/src/SettingsApi.php',
        'Enable\\Cors\\Traits\\Api' => __DIR__ . '/../..' . '/src/Traits/Api.php',
        'Enable\\Cors\\Upgrade' => __DIR__ . '/../..' . '/src/Upgrade.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit4e2e0fdcba009c0df25206a0075545aa::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit4e2e0fdcba009c0df25206a0075545aa::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit4e2e0fdcba009c0df25206a0075545aa::$classMap;

        }, null, ClassLoader::class);
    }
}
